import {<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, } from 'recharts';
import { getFacilityDashboardMetricsAPI } from '../../../api';

export default function FacilityDashboard() {


 


  

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Facility Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <PatientsCard />
        <FacilityStaffCard  />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FacilityDataCard />
        <MedicalRecordsChartCard  />
      </div>
    </div>
  );
}




function MedicalRecordsChartCard({ data }) {
  if (!data) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Medical Records</h2>
        <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
          <p className="text-gray-500">Loading medical records data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Medical Records</h2>
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-4xl font-bold text-green-600 mb-2">{data.last7Days || 0}</div>
          <div className="text-lg text-gray-600">Records Updated</div>
          <div className="text-sm text-gray-500">in the last 7 days</div>
        </div>
      </div>
    </div>
  );
}
function FacilityStaffCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Facility Staff</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading staff data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Facility Staff</h2>
      </div>

      <div className="flex justify-between items-center px-6 py-2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-users text-blue-600 text-3xl"></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">{data.totalWorkers || 0}</p>
        </div>
      </div>

      {/* Staff Composition Bar Graph - Kept original but with matched styling */}
      <div className="px-6 py-4">
        <h3 className="text-sm font-medium text-gray-600 mb-3">Staff Composition</h3>
        <div className='h-[30px] w-full bg-amber-100 flex rounded-[20px] overflow-hidden'>
          <div 
            className="admin h-full bg-blue-600 transition-all duration-500" 
            style={{ width: `${(data.admins / data.totalWorkers) * 100}%` }}
          ></div>
          <div 
            className="doc h-full bg-pink-600 transition-all duration-500" 
            style={{ width: `${(data.doctors / data.totalWorkers) * 100}%` }}
          ></div>
        </div>
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-6">
        <div className="flex flex-col gap-1">
          <p className="text-sm text-gray-600 font-medium">Doctors: {data.doctors || 0}</p>
          <p className="text-sm text-gray-600 font-medium">Admins: {data.admins || 0}</p>
        </div>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Admins</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Doctors</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function FacilityDataCard() {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Facility Data</h2>
      <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-gray-500">Facility data will be displayed here</p>
      </div>
    </div>
  );
}

function PatientsCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading patient data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
      </div>

      <div className="flex justify-between items-center px-6 py-2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-user-injured text-blue-600 text-3xl"></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">{data.total || 0}</p>
        </div>

        <div className="w-[100px] h-[100px] transform hover:scale-105 transition-transform">
          <WomanManPieChart />
        </div>
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-6">
        <p className="text-sm text-gray-600 font-medium">{data.last7Days || 0} in last 7 days</p>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">F</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">M</span>
          </div>
        </div>
      </div>
    </div>
  );
}



function WomanManPieChart(data) {
  const COLORS = ['#3b82f6', '#ec4899'];

  return (
    <PieChart width={100} height={100}>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        innerRadius={25}
        outerRadius={40}
        paddingAngle={2}
        dataKey="value"
        animationBegin={100}
        animationDuration={1000}
      >
        {data.map((_, index) => (
          <Cell
            key={`cell-${index}`}
            fill={COLORS[index % COLORS.length]}
            stroke="black"
            strokeWidth={1}
          />
        ))}
      </Pie>
    </PieChart>
  );
}





/**
 * ? Patient card needs:
 *  - number of patients for each gender from last month to now
 *  - total patients in last 7 days (not gender specific)
 * ? Staff card needs:
 *  - all category staff count as {type1:count,type2:count,...}
 * ? facility Card needs:
 * - name
 * - address
 * - phone
 * - image
 * - createdAt
 * - type
 * - facility manager name
 * - facility manager email
 * - facility manager phone
 * 
 * ? Medical records card needs:
 * - number of records( visits ) for each month of the selected Year
 */